"""
用户相关API接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime
import logging

from database import get_db_session
from models import User, InviteCode
from pydantic import BaseModel
from utils.auth import hash_password, authenticate_user, create_user_token
from middleware.auth import get_current_user, get_current_user_optional

logger = logging.getLogger(__name__)
router = APIRouter()


class UserRegisterRequest(BaseModel):
    """用户注册请求"""
    username: str
    email: Optional[str] = None
    password: str
    invite_code: Optional[str] = None


class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: str
    password: str


class UserInfoResponse(BaseModel):
    """用户信息响应"""
    id: int
    username: str
    email: Optional[str]
    status: str
    referrer_id: Optional[int]
    created_at: datetime


class UserRegisterResponse(BaseModel):
    """用户注册响应"""
    success: bool
    message: str
    user_id: Optional[int] = None


class UserLoginResponse(BaseModel):
    """用户登录响应"""
    success: bool
    message: str
    user_id: Optional[int] = None
    token: Optional[str] = None


@router.post("/register", response_model=UserRegisterResponse)
async def register_user(
    request: UserRegisterRequest,
    db: Session = Depends(get_db_session)
):
    """
    用户注册接口
    
    Args:
        request: 注册请求
        db: 数据库会话
        
    Returns:
        UserRegisterResponse: 注册结果
    """
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == request.username).first()
        if existing_user:
            return UserRegisterResponse(
                success=False,
                message="用户名已存在"
            )
        
        # 检查邮箱是否已存在（如果提供了邮箱）
        if request.email:
            existing_email = db.query(User).filter(User.email == request.email).first()
            if existing_email:
                return UserRegisterResponse(
                    success=False,
                    message="邮箱已被使用"
                )
        
        # 处理邀请码
        referrer_id = None
        if request.invite_code:
            invite_code = db.query(InviteCode).filter(
                InviteCode.code == request.invite_code,
                InviteCode.status == 'active'
            ).first()
            if not invite_code:
                return UserRegisterResponse(
                    success=False,
                    message="邀请码无效或已失效"
                )
            referrer_id = invite_code.user_id
        
        # 创建新用户，使用bcrypt加密密码
        password_hash = hash_password(request.password)
        new_user = User(
            username=request.username,
            email=request.email,
            password_hash=password_hash,
            referrer_id=referrer_id
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 如果使用了邀请码，标记为已使用
        if request.invite_code and invite_code:
            invite_code.used_by = new_user.id
            invite_code.used_at = datetime.now()
            invite_code.status = 'used'
            db.commit()
        
        return UserRegisterResponse(
            success=True,
            message="注册成功",
            user_id=new_user.id
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"用户注册失败: {e}")
        return UserRegisterResponse(
            success=False,
            message="注册失败，请稍后重试"
        )


@router.post("/login", response_model=UserLoginResponse)
async def login_user(
    request: UserLoginRequest,
    db: Session = Depends(get_db_session)
):
    """
    用户登录接口
    
    Args:
        request: 登录请求
        db: 数据库会话
        
    Returns:
        UserLoginResponse: 登录结果
    """
    try:
        # 验证用户名和密码
        user = authenticate_user(request.username, request.password, db)
        if not user:
            return UserLoginResponse(
                success=False,
                message="用户名或密码错误"
            )

        # 生成JWT token
        token = create_user_token(user)

        return UserLoginResponse(
            success=True,
            message="登录成功",
            user_id=user.id,
            token=token
        )
        
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        return UserLoginResponse(
            success=False,
            message="登录失败，请稍后重试"
        )


@router.get("/info/{user_id}", response_model=UserInfoResponse)
async def get_user_info(
    user_id: int,
    db: Session = Depends(get_db_session)
):
    """
    获取用户信息接口（公开接口，不需要认证）

    Args:
        user_id: 用户ID
        db: 数据库会话

    Returns:
        UserInfoResponse: 用户信息
    """
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        return UserInfoResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            status=user.status.value,
            referrer_id=user.referrer_id,
            created_at=user.created_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户{user_id}信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.get("/me", response_model=UserInfoResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息接口（需要认证）

    Args:
        current_user: 当前认证用户

    Returns:
        UserInfoResponse: 当前用户信息
    """
    return UserInfoResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        status=current_user.status.value,
        referrer_id=current_user.referrer_id,
        created_at=current_user.created_at
    )
